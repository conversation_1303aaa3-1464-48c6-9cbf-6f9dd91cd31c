using System;
using System.Collections.Generic;
using System.Linq;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Editor
{
    public class BhUITool : OdinEditorWindow
    {
        private const int _BUTTON_SIZE = 50;



        [MenuItem("b100/Open Bh UI Tool Panel", priority = 1)]
        public static void ShowWindow()
        {
            GetWindow<BhUITool>("UI Tool");
        }

        protected override void Initialize()
        {
            base.Initialize();
        }

        #region Select

        [Title("Info")]
        [TableList(DrawScrollView = false, NumberOfItemsPerPage = 5, ShowIndexLabels = false, ShowPaging = true)]
        public List<RectTransformItem> rectTransformSelectedItems;

        private List<GameObject> _gameObjects = new List<GameObject>();

        [Title("Settings")]
        [PropertySpace(0f, 5f)]
        [Tooltip("Only active objects are handler")]
        public bool onlySelectActiveObject = false;


        protected override void OnGUI()
        {
            base.OnGUI();


            if (Selection.gameObjects.Length != _gameObjects.Count)
            {
                UpdateSelectedGameObject();
            }
            else
            {
                var isCollectionChange = false;

                foreach (var gameObject in Selection.gameObjects)
                {
                    if (!_gameObjects.Contains(gameObject))
                    {
                        isCollectionChange = true;
                        break;
                    }
                }

                if (isCollectionChange)
                {
                    UpdateSelectedGameObject();
                }
            }

            void UpdateSelectedGameObject()
            {
                _gameObjects = Selection.gameObjects.ToList();
                
                rectTransformSelectedItems.Clear();

                foreach (var gameObject in _gameObjects)
                {
                    if (onlySelectActiveObject && !gameObject.activeSelf)
                        continue;
                    
                    var rectTransform = gameObject.GetComponent<RectTransform>();

                    if (rectTransform != null)
                    {
                        var item = new RectTransformItem
                        {
                            rectTransform = rectTransform,
                        };
                        
                        rectTransformSelectedItems.Add(item);
                    }
                    else
                    {
                        Debug.LogError("Not found Rect Transform on the selected game object: " + gameObject.name);
                    }
                }
            }
        }


        #endregion



        #region Anchor

        /*[Button]
        void ShowInfo()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                DebugLogInfo(rectTransform.rectTransform);
            }
            
            void DebugLogInfo(RectTransform rectTransform)
            {
                Debug.Log("Anchor min: " + rectTransform.anchorMin);
                Debug.Log("Anchor max: " + rectTransform.anchorMax);
                Debug.Log("Offset min: " + rectTransform.offsetMin);
                Debug.Log("Offset max: " + rectTransform.offsetMax);
            }
        }*/


        [Title("Anchor")]
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderOuter, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = "")]
        void AnchorFull()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
               SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );
                
                rectTransform.anchorMin = newAnchorMin;
                rectTransform.anchorMax = newAnchorMax;
                
                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = Vector2.zero;
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderTop, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Horizontal")]
        void AnchorHorizontalTop()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );
                
                rectTransform.anchorMin = newAnchorMin;
                rectTransform.anchorMax = newAnchorMax;
                
                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = Vector2.zero;
                
                rectTransform.anchorMin = new Vector2(newAnchorMin.x, newAnchorMax.y);

                rectTransform.offsetMin = new Vector2(0f, -initHeight);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderCenter, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Horizontal")]
        [ExecuteInEditMode]
        void AnchorHorizontalMid()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );
                
                rectTransform.anchorMin = new Vector2(newAnchorMin.x, (newAnchorMin.y + newAnchorMax.y) / 2f);
                rectTransform.anchorMax = new Vector2(newAnchorMax.x, (newAnchorMin.y + newAnchorMax.y) / 2f);

                rectTransform.offsetMin = new Vector2(0f, -initHeight / 2f);
                rectTransform.offsetMax = new Vector2(0f, initHeight / 2f);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderBottom, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Horizontal")]
        void AnchorHorizontalBottom()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );
                
                rectTransform.anchorMin = newAnchorMin;
                rectTransform.anchorMax = newAnchorMax;
                
                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = Vector2.zero;
                
                rectTransform.anchorMax = new Vector2(newAnchorMax.x, newAnchorMin.y);

                rectTransform.offsetMax = new Vector2(0f, initHeight);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderLeft, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Vertical")]
        void AnchorVerticalLeft()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );

                rectTransform.anchorMin = newAnchorMin;
                rectTransform.anchorMax = new Vector2(newAnchorMin.x, newAnchorMax.y);

                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = new Vector2(initWidth, 0f);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderMiddle, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Vertical")]
        void AnchorVerticalMid()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );
                
                rectTransform.anchorMin = new Vector2((newAnchorMin.x + newAnchorMax.x) / 2f, newAnchorMin.y);
                rectTransform.anchorMax = new Vector2((newAnchorMin.x + newAnchorMax.x) / 2f, newAnchorMax.y);

                rectTransform.offsetMin = new Vector2(-initWidth / 2f, 0f);
                rectTransform.offsetMax = new Vector2(initWidth / 2f, 0f);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.BorderRight, ButtonAlignment = 1, IconAlignment = IconAlignment.RightOfText, Name = ""), HorizontalGroup("Vertical")]
        void AnchorVerticalRight()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAnchorsToCurrentPosition(rectTransform.rectTransform);
            }
            
            void SetAnchorsToCurrentPosition(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var initWidth = rectTransform.rect.width;
                var initHeight = rectTransform.rect.height;
                
                Vector2 newAnchorMin = new Vector2(
                    rectTransform.anchorMin.x + rectTransform.offsetMin.x / parentRectTransform.rect.width,
                    rectTransform.anchorMin.y + rectTransform.offsetMin.y / parentRectTransform.rect.height
                );

                Vector2 newAnchorMax = new Vector2(
                    rectTransform.anchorMax.x + rectTransform.offsetMax.x / parentRectTransform.rect.width,
                    rectTransform.anchorMax.y + rectTransform.offsetMax.y / parentRectTransform.rect.height
                );

                rectTransform.anchorMin = new Vector2(newAnchorMax.x, newAnchorMin.y);
                rectTransform.anchorMax = newAnchorMax;

                rectTransform.offsetMin = new Vector2(-initWidth, 0f);
                rectTransform.offsetMax = Vector2.zero;
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        
        #endregion



        #region Alignment


        [PropertySpace(5f, 0f)]
        [Title("Alignment")]
        [Button(Style = ButtonStyle.Box, ButtonHeight = 0, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        void Free()
        {
            // No implement
        }
        
        //[PropertySpace(20f, 0f)]
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignTop, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentVertical")]
        void AlignTop()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentHeight = parentRectTransform.rect.height;
                var height = rectTransform.rect.height;

                var correctPositionY = parentHeight / 2f - height / 2f;

                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, correctPositionY);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        //[PropertySpace(20f, 0f)]
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignMiddle, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentVertical")]
        void AlignMiddle()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentHeight = parentRectTransform.rect.height;
                var height = rectTransform.rect.height;

                var correctPositionY = parentHeight / 2f;

                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, 0f);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        //[PropertySpace(20f, 0f)]
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignBottom, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentVertical")]
        void AlignBottom()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentHeight = parentRectTransform.rect.height;
                var height = rectTransform.rect.height;

                var correctPositionY = parentHeight / 2f - height / 2f;

                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, -correctPositionY);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignStart, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentHorizontal")]
        void AlignLeft()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentWidth = parentRectTransform.rect.width;
                var width = rectTransform.rect.width;

                var correctPositionX = parentWidth / 2f - width / 2f;

                rectTransform.localPosition = new Vector2(-correctPositionX, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignCenter, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentHorizontal")]
        void AlignCenter()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentWidth = parentRectTransform.rect.width;
                var width = rectTransform.rect.width;

                var correctPositionX = parentWidth / 2f;

                rectTransform.localPosition = new Vector2(0f, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.AlignEnd, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentHorizontal")]
        void AlignRight()
        {
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                SetAlignment(rectTransform.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                RectTransform parentRectTransform = rectTransform.parent as RectTransform;

                if (parentRectTransform == null)
                {
                    Debug.LogError("Parent is not a RectTransform.");
                    return;
                }
                
                Undo.RecordObject(rectTransform, "Set Anchors");

                var parentWidth = parentRectTransform.rect.width;
                var width = rectTransform.rect.width;

                var correctPositionX = parentWidth / 2f - width / 2f;

                rectTransform.localPosition = new Vector2(correctPositionX, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        


        #endregion



        #region Alignment Item

        [PropertySpace(5f, 0f)]
        [Title("Alignment Item")]
        [Button(Style = ButtonStyle.Box, ButtonHeight = 0, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        void Free2()
        {
            // No implement
        }


        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.ArrowBarUp, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemVertical")]
        void ToTop()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }

            var minHeight = Mathf.Infinity;
            
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                var height = rectTransform.rectTransform.rect.height;
                var bottom = rectTransform.rectTransform.localPosition.y - height / 2f;
                
                if (bottom < minHeight)
                {
                    minHeight = bottom;
                }
            }

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var height = rectTransform.rect.height;
                var correctPositionY = minHeight + height / 2f;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, correctPositionY);
                
                EditorUtility.SetDirty(rectTransform);
            }

        }

        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.ArrowsCollapse,
            ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemVertical")]
        void ToMid()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }

            var average = rectTransformSelectedItems.Average(x => x.rectTransform.localPosition.y);

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var correctPositionY = average;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, correctPositionY);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.ArrowBarDown, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemVertical")]
        void ToBottom()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }
            
            var maxHeight = 0f;
            
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                var height = rectTransform.rectTransform.rect.height;
                var top = rectTransform.rectTransform.localPosition.y + height / 2f;
                
                if (top > maxHeight)
                {
                    maxHeight = top;
                }
            }

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var height = rectTransform.rect.height;
                var correctPositionY = maxHeight - height / 2f;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(rectTransform.localPosition.x, correctPositionY);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.ArrowBarLeft, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemHorizontal")]
        void ToLeft()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }

            var maxWidth = 0f;
            
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                var width = rectTransform.rectTransform.rect.width;
                var right = rectTransform.rectTransform.localPosition.x + width / 2f;
                
                if (right > maxWidth)
                {
                    maxWidth = right;
                }
            }

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var width = rectTransform.rect.width;
                var correctPositionX = maxWidth - width / 2f;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(correctPositionX, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }

        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.DistributeVertical,
            ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemHorizontal")]
        void ToCenter()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }

            var average = rectTransformSelectedItems.Average(x => x.rectTransform.localPosition.x);

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var correctPositionX = average;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(correctPositionX, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }
        
        [Button(Style = ButtonStyle.Box, ButtonHeight = _BUTTON_SIZE, Icon = SdfIconType.ArrowBarRight, ButtonAlignment = 1,
            IconAlignment = IconAlignment.RightOfText, Name = "")]
        [HorizontalGroup("AlignmentItemHorizontal")]
        void ToRight()
        {
            if (rectTransformSelectedItems.Count < 2)
            {
                Debug.LogError("At least 2 rect transforms are required for alignment!");
                return;
            }

            var minWidth = Mathf.Infinity;
            
            foreach (var rectTransform in rectTransformSelectedItems)
            {
                var width = rectTransform.rectTransform.rect.width;
                var left = rectTransform.rectTransform.localPosition.x - width / 2f;
                
                if (left < minWidth)
                {
                    minWidth = left;
                }
            }

            foreach (var rectTransformItem in rectTransformSelectedItems)
            {
                SetAlignment(rectTransformItem.rectTransform);
            }
            
            void SetAlignment(RectTransform rectTransform)
            {
                var width = rectTransform.rect.width;
                var correctPositionX = minWidth + width / 2f;
                
                Undo.RecordObject(rectTransform, "AlignmentItem");
                
                rectTransform.localPosition = new Vector2(correctPositionX, rectTransform.localPosition.y);
                
                EditorUtility.SetDirty(rectTransform);
            }
        }


        #endregion
        
        
        [Serializable]
        public class RectTransformItem
        {
            public RectTransform rectTransform;
        }

    }
    
    
}
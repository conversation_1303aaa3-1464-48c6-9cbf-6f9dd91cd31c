using System.Linq;
using b100SDK.Scripts.Utilities.Tool;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

namespace b100SDK.Scripts.Editor
{
    public class BhSdkPanel : OdinMenuEditorWindow
    {
        private const string IconMenuPath = "Assets/b100SDK/SDK Resources/IconSDK/";
        
        
        private Sprite _iconGameSetting; 
        private Sprite _iconGameConfig; 
        private Sprite _iconSoundConfig; 
        private Sprite _iconUiConfig; 
        private Sprite _iconIAPConfig; 
        private Sprite _iconIAAConfig; 
        private Sprite _iconRemoteConfig; 
        private Sprite _iconBuilder; 
        
        
        
        
        [MenuItem("b100/Open Bh SDK Panel", priority = 0)]
        public static void OpenWindow()
        {
            GetWindow<BhSdkPanel>().Show();
        }

        protected override OdinMenuTree BuildMenuTree()
        {
            var tree = new OdinMenuTree();

            tree.Add("Game Setting", SettingTool.GetGameSetting(), _iconGameSetting);
            tree.Add("Game Config", ConfigTool.GetGameConfig(), _iconGameConfig);
            tree.Add("Sound Config", ConfigTool.GetSoundConfig(), _iconSoundConfig);
            tree.Add("UI Config", ConfigTool.GetEditorUiConfig(), _iconUiConfig);
            tree.Add("IAP Config", ConfigTool.GetIAPConfig(), _iconIAPConfig);
            tree.Add("IAA Config", ConfigTool.GetIAAConfig(), _iconIAAConfig);
            tree.Add("Remote Config", ConfigTool.GetRemoteConfigGroup(), _iconRemoteConfig);
            tree.Add("Builder", BuilderTool.GetGameBuilder(), _iconBuilder);

            return tree;
        }

        protected override void Initialize()
        {
            base.Initialize();

            var sprites = UtilitiesTool.GetSprite(IconMenuPath);

            _iconGameSetting = sprites.FirstOrDefault(x => x.name == "IconGameSetting");
            _iconGameConfig = sprites.FirstOrDefault(x => x.name == "IconGameConfig");
            _iconSoundConfig = sprites.FirstOrDefault(x => x.name == "IconSoundConfig");
            _iconUiConfig = sprites.FirstOrDefault(x => x.name == "IconUiConfig");
            _iconIAPConfig = sprites.FirstOrDefault(x => x.name == "IconIAPConfig");
            _iconIAAConfig = sprites.FirstOrDefault(x => x.name == "IconIAAConfig");
            _iconRemoteConfig = sprites.FirstOrDefault(x => x.name == "IconRemoteConfig");
            _iconBuilder = sprites.FirstOrDefault(x => x.name == "IconBuilder");
        }
    }
}
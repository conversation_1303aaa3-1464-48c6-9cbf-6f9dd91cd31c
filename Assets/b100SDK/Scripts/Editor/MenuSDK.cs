#if UNITY_EDITOR

using b100SDK.Scripts.Base;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace b100SDK.Scripts.Editor
{
    public class MenuSDK : BhMonoBehavior
    {
        #region Scene

        private const string _DEFAULT_PATH_SCENE_FOLDER = "Assets/b100SDK/Scenes/";
        private const string _SPLASH_SCENE_NAME = "Splash";
        private const string _GAMEPLAY_SCENE_NAME = "Gameplay";
        private const string _TEST_SCENE_NAME = "Test";
        
        [MenuItem("b100/Open Scene/Open Splash")]
        private static void OpenSplashScene()
        {
            OpenScene(_SPLASH_SCENE_NAME);
        }

        [MenuItem("b100/Open Scene/Open Gameplay")]
        private static void OpenGameplayScene()
        {
            OpenScene(_GAMEPLAY_SCENE_NAME);
        }
        
        [MenuItem("b100/Open Scene/Open Test")]
        private static void OpenTestScene()
        {
            OpenScene(_TEST_SCENE_NAME);
        }
        private static void OpenScene(string sceneName)
        {
            if (sceneName != _TEST_SCENE_NAME && !SceneExistsInBuildSettings(sceneName))
            {
                Debug.LogWarning("Scene does not exist in build settings: " + sceneName);
                return;
            }
            
            if (EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo())
            {
                EditorSceneManager.OpenScene(_DEFAULT_PATH_SCENE_FOLDER + sceneName + ".unity");
            }
        }
        
        private static bool SceneExistsInBuildSettings(string sceneName)
        {
            EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;

            foreach (EditorBuildSettingsScene scene in scenes)
            {
                if (scene.path.Contains(sceneName))
                {
                    return true;
                }
            }

            return false;
        }
        
        #endregion
    }
}

#endif
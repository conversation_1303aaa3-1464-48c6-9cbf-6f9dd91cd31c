using System;
using UnityEngine;

namespace ES3Types
{
	[UnityEngine.Scripting.Preserve]
	[ES3PropertiesAttribute([propertyNames])]
	public class ES3UserType_[es3TypeSuffix] : ES3ComponentType
	{
		public static ES3Type Instance = null;

		public ES3UserType_[es3TypeSuffix]() : base(typeof([fullType])){ Instance = this; priority = 1;}


		protected override void WriteComponent(object obj, ES3Writer writer)
		{
			var instance = ([fullType])obj;
			[writes]
		}

		protected override void ReadComponent<T>(ES3Reader reader, object obj)
		{
			var instance = ([fullType])obj;
			foreach(string propertyName in reader.Properties)
			{
				switch(propertyName)
				{
					[reads]
					default:
						reader.Skip();
						break;
				}
			}
		}
	}


	public class ES3UserType_[es3TypeSuffix]Array : ES3ArrayType
	{
		public static ES3Type Instance;

		public ES3UserType_[es3TypeSuffix]Array() : base(typeof([fullType][]), ES3UserType_[es3TypeSuffix].Instance)
		{
			Instance = this;
		}
	}
}
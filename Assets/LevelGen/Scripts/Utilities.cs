using System.Collections.Generic;
using b100SDK.Scripts.Utilities;

namespace LevelGen.Scripts
{
    public static class Utilities
    {
        public static void PrintMatrix(this int[,] matrix)
        {
            int rows = matrix.GetLength(0);
            int cols = matrix.GetLength(1);
            BhDebug.Log("=======================================");
            var s = "";
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    s += matrix[i, j] + " ";
                }
                s += "\n";
            }
            BhDebug.Log(s);
            BhDebug.Log("=======================================");
        }
        
        public static void PrintMatrix(this int[,,] matrix)
        {
            BhDebug.Log("=======================================");
            int depth = matrix.GetLength(0);
            int rows = matrix.GetLength(1);
            int cols = matrix.GetLength(2);
            for (int d = 0; d < depth; d++)
            {
                BhDebug.Log($"Layer {d}:");
                var s = "";
                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < cols; j++)
                    {
                        s += matrix[d, i, j] + " ";
                    }
                    s += "\n";
                }
                BhDebug.Log(s);
            }
            BhDebug.Log("=======================================");
        }

    }
    
    [System.Serializable]
    public class Grid3DSavable<T>
    {
        public int depth, rows, cols;
        public List<T> flatData;
    }

    public static class Grid3DConverter
    {
        public static Grid3DSavable<T> ToSave<T>(this T[,,] array)
        {
            int d = array.GetLength(0);
            int r = array.GetLength(1);
            int c = array.GetLength(2);

            var save = new Grid3DSavable<T>
            {
                depth = d,
                rows = r,
                cols = c,
                flatData = new List<T>(d * r * c)
            };

            for (int z = 0; z < d; z++)
            for (int y = 0; y < r; y++)
            for (int x = 0; x < c; x++)
                save.flatData.Add(array[z, y, x]);

            return save;
        }

        public static T[,,] ToArray<T>(this Grid3DSavable<T> savable)
        {
            var array = new T[savable.depth, savable.rows, savable.cols];
            int index = 0;
            for (int z = 0; z < savable.depth; z++)
            for (int y = 0; y < savable.rows; y++)
            for (int x = 0; x < savable.cols; x++)
                array[z, y, x] = savable.flatData[index++];
            return array;
        }
    }
}
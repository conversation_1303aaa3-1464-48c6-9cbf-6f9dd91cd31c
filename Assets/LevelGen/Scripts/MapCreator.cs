using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using b100SDK.Scripts.Utilities;
using LibNoise.Primitive;
using Sirenix.OdinInspector;
using UnityEngine;
using Random = UnityEngine.Random;

namespace LevelGen.Scripts
{
    public class MapCreator : MonoBehaviour
    {
        [Title("Setting")]
        [SerializeField]
        private float minScale = 100f;
        
        [SerializeField]
        private float maxScale = 1000f;
        
        [SerializeField]
        private MapCreatorUtilities.SymmetryType symmetryType = MapCreatorUtilities.SymmetryType.Horizontal;
        
        [SerializeField]
        private float minThreshold = -.8f;
        
        [SerializeField]
        private float maxThreshold = .99f;
        
        
        [PropertySpace(20f)]
        [SerializeField]
        private GridSetting gridSetting;


        private CancellationTokenSource _cancellationTokenSource;




        [Button]
        async void Test()
        {
            /*var data = CreateMapByThreshold(2, 5, 6, new List<float> {-.5f, 0f, .5f}, 20, 1f);
            data.PrintMatrix();*/
            
            var data = await CreateMapByThreshold(5, 6, 20, .5f);
            
            if (data == null)
            {
                BhDebug.Log("Data is null");
                return;
            }
            
            data.PrintMatrix();
        }

        [Button]
        void Stop()
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
            }
        }
        
        
        
        int[,,] CreateMapByThreshold(int depth, int row, int col, List<float> thresholds, int tileCount, float offsetTileCount)
        {
            switch (symmetryType)
            {
                case 0:
                    row *= 2;
                    col *= 2;
                    break;
                case MapCreatorUtilities.SymmetryType.Horizontal:
                    row *= 2;
                    break;
                case MapCreatorUtilities.SymmetryType.Vertical:
                    col *= 2;
                    break;
            }
            
            thresholds.Sort();
            
            while (true)
            {
                var matrix2DList = new List<int[,]>();
                var randomScaleMin = -Random.Range(minScale, maxScale);
                var randomScaleMax = Random.Range(minScale, maxScale);
                var scale = Random.Range(randomScaleMin, randomScaleMax);

                var noise = new SimplexPerlin();

                for (int d = 0; d < depth; d++)
                {
                    var threshold = thresholds[d];
                
                    var matrix = new float[row, col];
                    var temp = new int[row, col];

                    for (int i = 0; i < row; i++)
                    {
                        for (int j = 0; j < col; j++)
                        {
                            matrix[i, j] = noise.GetValue(i * scale, j * scale, d * scale);

                            if (matrix[i, j] >= threshold)
                            {
                                temp[i, j] = 1;
                            }
                            else
                            {
                                temp[i, j] = 0;
                            }
                        }
                    }

                    var result = temp.GetSymmetryMatrix(symmetryType);
                    
                    result.PrintMatrix();
                
                    matrix2DList.Add(result);
                }

                if (matrix2DList.Count == 0)
                {
                    continue;
                }

                var matrix3D = ConvertTo3DMatrix(matrix2DList);
                
                if (matrix3D == null)
                {
                    continue;
                }
                
                matrix3D = ValidateMatrix(matrix3D);


                var levelMap = matrix3D.FindSquaresSymmetric3D(out var count);

                if (count % 2 != 0)
                {
                    continue;
                }
                
                var offsetRate = (float)Mathf.Abs(count - tileCount) / tileCount;
                if (offsetRate < offsetTileCount)
                {
                    return levelMap;
                }
            }
        }        
        
        
        async Task<int[,,]> CreateMapByThreshold(int row, int col, int tileCount, float offsetTileCount, int attemptCount = 1000)
        {
            _cancellationTokenSource = new CancellationTokenSource();
            
            switch (symmetryType)
            {
                case 0:
                    row *= 2;
                    col *= 2;
                    break;
                case MapCreatorUtilities.SymmetryType.Horizontal:
                    row *= 2;
                    break;
                case MapCreatorUtilities.SymmetryType.Vertical:
                    col *= 2;
                    break;
            }
            
            var minTileCount = Mathf.RoundToInt(tileCount * (1 - offsetTileCount));
            var maxTileCount = Mathf.RoundToInt(tileCount * (1 + offsetTileCount));

            var correctTileCount = 0;

            int[,,] levelMap = null;
            
            var attempt = 0;
            
            while (attempt < attemptCount)
            {
                if (_cancellationTokenSource.IsCancellationRequested)
                {
                    BhDebug.Log("Cancel");
                    return null;
                }
                
                BhDebug.Log($"Attempt: {attempt} =========================");
                var matrix2DList = new List<int[,]>();
                var randomScaleMin = -Random.Range(minScale, maxScale);
                var randomScaleMax = Random.Range(minScale, maxScale);
                var scale = Random.Range(randomScaleMin, randomScaleMax);

                var noise = new SimplexPerlin();
                var d = -1;

                while (true)
                {
                    if (_cancellationTokenSource.IsCancellationRequested)
                    {
                        BhDebug.Log("Cancel");
                        return null;
                    }
                    
                    // Thêm layer mới
                    d++;
                    var threshold = Random.Range(minThreshold, maxThreshold);
                    
                    var matrix = new float[row, col];
                    var temp = new int[row, col];

                    for (int i = 0; i < row; i++)
                    {
                        for (int j = 0; j < col; j++)
                        {
                            matrix[i, j] = noise.GetValue(i * scale, j * scale, d * scale);

                            if (matrix[i, j] >= threshold)
                            {
                                temp[i, j] = 1;
                            }
                            else
                            {
                                temp[i, j] = 0;
                            }
                        }
                    }

                    var result = temp.GetSymmetryMatrix(symmetryType);
                    
                    result.PrintMatrix();
                
                    matrix2DList.Add(result);
                    
                    var matrix3D = ConvertTo3DMatrix(matrix2DList);
                    
                    if (matrix3D == null)
                    {
                        attempt++;
                        await Task.Delay(1);
                        continue;
                    }
                    
                    matrix3D = ValidateMatrix(matrix3D);
                    
                    levelMap = matrix3D.FindSquaresSymmetric3D(out var count);
                    
                    BhDebug.Log($"Count: {count}");

                    if (count % 2 != 0)
                    {
                        attempt++;
                        await Task.Delay(1);
                        continue;
                    }
                    
                    if (count >= minTileCount && count <= maxTileCount)
                    {
                        correctTileCount = count;
                        break;
                    }
                    
                    attempt++;
                    await Task.Delay(1);
                }
            }
            
            return levelMap;
        }
        
        private static int[,,] ConvertTo3DMatrix(List<int[,]> matrices)
        {
            if (matrices == null || matrices.Count == 0)
                throw new ArgumentException("Danh sách ma trận không được rỗng");

            int depth = matrices.Count;
            int rows = matrices[0].GetLength(0);
            int cols = matrices[0].GetLength(1);

            int[,,] matrix3D = new int[depth, rows, cols];

            for (int d = 0; d < depth; d++)
            {
                if (matrices[d].GetLength(0) != rows || matrices[d].GetLength(1) != cols)
                    throw new ArgumentException("Tất cả các ma trận phải có cùng kích thước");

                for (int i = 0; i < rows; i++)
                {
                    for (int j = 0; j < cols; j++)
                    {
                        matrix3D[d, i, j] = matrices[d][i, j];
                    }
                }
            }

            return matrix3D;
        }
        private static int[,,] ValidateMatrix(int[,,] matrix)
        {
            // Loại bỏ đi các layer mà ko có tile nào, tức toàn bộ layer đều là 0
            var depth = matrix.GetLength(0);
            var row = matrix.GetLength(1);
            var col = matrix.GetLength(2);

            var d = 0;
            while (d < depth)
            {
                var check = false;
                
                for (int r = 0; r < row; r++)
                {
                    for (int c = 0; c < col; c++)
                    {
                        if (matrix[d, r, c] != 0)
                        {
                            check = true;
                            break;
                        }
                    }
                }

                if (!check)
                {
                    // Loại bỏ layer này
                    matrix = RemoveLayer(matrix, d);
                    depth = matrix.GetLength(0);
                }
                else
                {
                    d++;
                }
            }
            
            return matrix;
        }

        private static int[,,] RemoveLayer(int[,,] matrix, int i)
        {
            var depth = matrix.GetLength(0);
            var row = matrix.GetLength(1);
            var col = matrix.GetLength(2);

            int[,,] newMatrix = new int[depth - 1, row, col];

            for (int d = 0; d < depth; d++)
            {
                if (d < i)
                {
                    for (int r = 0; r < row; r++)
                    {
                        for (int c = 0; c < col; c++)
                        {
                            newMatrix[d, r, c] = matrix[d, r, c];
                        }
                    }
                }
                else if (d > i)
                {
                    for (int r = 0; r < row; r++)
                    {
                        for (int c = 0; c < col; c++)
                        {
                            newMatrix[d - 1, r, c] = matrix[d, r, c];
                        }
                    }
                }
            }

            return newMatrix;
        }
    }
}
// This file is part of YamlDotNet - A .NET library for YAML.
// Copyright (c) <PERSON> and contributors
//
// Permission is hereby granted, free of charge, to any person obtaining a copy of
// this software and associated documentation files (the "Software"), to deal in
// the Software without restriction, including without limitation the rights to
// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
// of the Software, and to permit persons to whom the Software is furnished to do
// so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.

using System;
using System.Collections.Generic;
using System.Globalization;
using YamlDotNet.Core;

namespace YamlDotNet.Serialization.ObjectGraphVisitors
{
    public sealed class AnchorAssigner : PreProcessingPhaseObjectGraphVisitorSkeleton, IAliasProvider
    {
        private class AnchorAssignment
        {
            public AnchorName Anchor;
        }

        private readonly Dictionary<object, AnchorAssignment> assignments = new ();
        private uint nextId;

        public AnchorAssigner(IEnumerable<IYamlTypeConverter> typeConverters)
            : base(typeConverters)
        {
        }

        protected override bool Enter(IObjectDescriptor value, ObjectSerializer serializer)
        {
            if (value.Value != null && assignments.TryGetValue(value.Value, out var assignment))
            {
                if (assignment.Anchor.IsEmpty)
                {
                    assignment.Anchor = new AnchorName("o" + nextId.ToString(CultureInfo.InvariantCulture));
                    ++nextId;
                }
                return false;
            }

            return true;
        }

        protected override bool EnterMapping(IObjectDescriptor key, IObjectDescriptor value, ObjectSerializer serializer)
        {
            return true;
        }

        protected override bool EnterMapping(IPropertyDescriptor key, IObjectDescriptor value, ObjectSerializer serializer)
        {
            return true;
        }

        protected override void VisitScalar(IObjectDescriptor scalar, ObjectSerializer serializer)
        {
            // Do not assign anchors to scalars
        }

        protected override void VisitMappingStart(IObjectDescriptor mapping, Type keyType, Type valueType, ObjectSerializer serializer)
        {
            VisitObject(mapping);
        }

        protected override void VisitMappingEnd(IObjectDescriptor mapping, ObjectSerializer serializer) { }

        protected override void VisitSequenceStart(IObjectDescriptor sequence, Type elementType, ObjectSerializer serializer)
        {
            VisitObject(sequence);
        }

        protected override void VisitSequenceEnd(IObjectDescriptor sequence, ObjectSerializer serializer) { }

        private void VisitObject(IObjectDescriptor value)
        {
            if (value.Value != null)
            {
                assignments.Add(value.Value, new AnchorAssignment());
            }
        }

        AnchorName IAliasProvider.GetAlias(object target)
        {
            if (target != null && assignments.TryGetValue(target, out var assignment))
            {
                return assignment.Anchor;
            }
            return AnchorName.Empty;
        }
    }
}
